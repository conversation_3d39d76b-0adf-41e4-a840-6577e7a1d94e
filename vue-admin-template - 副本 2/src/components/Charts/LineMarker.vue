<template>
  <div :id="id" :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'
import axios from 'axios'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    }
  },
  data() {
    return {
      chart: null,
      statisticsData: null
    }
  },
  mounted() {
    this.fetchStatisticsData()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async fetchStatisticsData() {
      try {
        const response = await axios.get('http://127.0.0.1:5002/api/statistics/age-disease')
        this.statisticsData = response.data
        this.initChart()
      } catch (error) {
        console.error('Error fetching age-disease statistics:', error)
        // 如果API调用失败，使用默认数据
        this.initChart()
      }
    },
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))

      // 如果有统计数据，使用真实数据；否则使用默认数据
      const ageGroups = this.statisticsData ? this.statisticsData.ageGroups : ['<=45', '45-55', '55-65', '65-75', '>75']
      const diseaseData = this.statisticsData ? this.statisticsData.diseaseData : this.getDefaultData()

      this.chart.setOption({
        backgroundColor: '#394056',
        title: {
          top: 20,
          text: '系统诊断对比图',   //修改数据表名
          textStyle: {
            fontWeight: 'normal',
            fontSize: 16,
            color: '#F1F1F3'
          },
          left: '1%'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#57617B'
            }
          }
        },
        legend: {
          top: 20,
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: ['总患病情况', '正常', '糖尿病', '青光眼', '白内障', 'AMD', '高血压', '近视', '其他疾病'], // 删除“男性患病”和“女性患病”
          right: '4%',
          textStyle: {
            fontSize: 12,
            color: '#F1F1F3'
          }
        },
        grid: {
          top: 100,
          left: '2%',
          right: '2%',
          bottom: '2%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#FFFFFF' // 修改轴线颜色为白色
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 14, // 增加字体大小
              color: '#FFFFFF' // 修改字体颜色为白色
            }
          },
          name: '年龄段', // 修改x轴名称
          data: ageGroups // 使用动态数据
        }],
        yAxis: [{
          type: 'value',
          name: '人',    //修改y轴单位
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#FFFFFF' // 修改轴线颜色为白色
            }
          },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 14, // 增加字体大小
              color: '#FFFFFF' // 修改字体颜色为白色
            }
          },
          splitLine: {
            lineStyle: {
              color: '#57617B'
            }
          }
        }],
        series: [
          {
            name: '总患病情况', // 修改折线名称
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                width: 1
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(137, 189, 27, 0.3)' },
                  { offset: 0.8, color: 'rgba(137, 189, 27, 0)' }
                ], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 10
              }
            },
            itemStyle: {
              normal: {
                color: 'rgb(137,189,27)',
                borderColor: 'rgba(137,189,2,0.27)',
                borderWidth: 12
              }
            },
            data: [415,
788,
1292,
769,
199
] // 第一个数据
          },
          // 删除“男性患病”和“女性患病”的配置
          {
            name: '正常',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(0, 255, 0, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(0,255,0)' } },
            data: [176,
315,
405,
204,
36
] // 示例数据
          },
          {
            name: '糖尿病',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(255, 165, 0, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(255,165,0)' } },
            data: [51,
131,
219,
104,
20
] // 示例数据
          },
          {
            name: '青光眼',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(0, 0, 255, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(0,0,255)' } },
            data: [21,
35,
63,
70,
26
] // 示例数据
          },
          {
            name: '白内障',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(128, 0, 128, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(128,0,128)' } },
            data: [9,
26,
62,
66,
48
] // 示例数据
          },
          {
            name: 'AMD',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(255, 0, 0, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(255,0,0)' } },
            data: [14,
31,
56,
47,
13
] // 示例数据
          },
          {
            name: '高血压',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(0, 255, 255, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(0,255,255)' } },
            data: [6,
29,
36,
6,
0
] // 示例数据
          },
          {
            name: '近视',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(255, 192, 203, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(255,192,203)' } },
            data: [26,
26,
58,
49,
15
] // 示例数据
          },
          {
            name: '其他疾病',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: { normal: { width: 1 } },
            areaStyle: { normal: { color: 'rgba(128, 128, 128, 0.3)' } },
            itemStyle: { normal: { color: 'rgb(128,128,128)' } },
            data: [112,
195,
393,
223,
41
] // 示例数据
          }
        ]
      })
    },
    getDefaultData() {
      // 默认硬编码数据，当API调用失败时使用
      return {
        '总患病情况': [415, 788, 1292, 769, 199],
        '正常': [176, 315, 405, 204, 36],
        '糖尿病': [51, 131, 219, 104, 20],
        '青光眼': [21, 35, 63, 70, 26],
        '白内障': [9, 26, 62, 66, 48],
        'AMD': [14, 31, 56, 47, 13],
        '高血压': [6, 29, 36, 6, 0],
        '近视': [26, 26, 58, 49, 15],
        '其他疾病': [112, 195, 393, 223, 41]
      }
    },
    createSeriesConfig(diseaseData) {
      const colors = {
        '总患病情况': 'rgb(137,189,27)',
        '正常': 'rgb(0,255,0)',
        '糖尿病': 'rgb(255,165,0)',
        '青光眼': 'rgb(0,0,255)',
        '白内障': 'rgb(128,0,128)',
        'AMD': 'rgb(255,0,0)',
        '高血压': 'rgb(0,255,255)',
        '近视': 'rgb(255,192,203)',
        '其他疾病': 'rgb(128,128,128)'
      }

      const series = []

      // 计算总患病情况
      const totalData = new Array(5).fill(0)
      Object.keys(diseaseData).forEach(disease => {
        if (disease !== '正常') {
          diseaseData[disease].forEach((count, index) => {
            totalData[index] += count
          })
        }
      })

      // 添加总患病情况系列
      series.push({
        name: '总患病情况',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 5,
        showSymbol: false,
        lineStyle: { normal: { width: 1 } },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(137, 189, 27, 0.3)' },
              { offset: 0.8, color: 'rgba(137, 189, 27, 0)' }
            ], false),
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10
          }
        },
        itemStyle: {
          normal: {
            color: colors['总患病情况'],
            borderColor: 'rgba(137,189,2,0.27)',
            borderWidth: 12
          }
        },
        data: totalData
      })

      // 添加各种疾病系列
      Object.keys(diseaseData).forEach(disease => {
        const color = colors[disease] || 'rgb(128,128,128)'
        series.push({
          name: disease,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 5,
          showSymbol: false,
          lineStyle: { normal: { width: 1 } },
          areaStyle: { normal: { color: color.replace('rgb', 'rgba').replace(')', ', 0.3)') } },
          itemStyle: { normal: { color: color } },
          data: diseaseData[disease]
        })
      })

      return series
    }
  }
}

</script>
