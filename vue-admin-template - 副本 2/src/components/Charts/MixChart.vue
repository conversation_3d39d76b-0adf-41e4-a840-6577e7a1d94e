<template>
  <div :id="id" :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'
import axios from 'axios'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    }
  },
  data() {
    return {
      chart: null,
      statisticsData: null
    }
  },
  mounted() {
    this.fetchStatisticsData()
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    async fetchStatisticsData() {
      try {
        const response = await axios.get('http://127.0.0.1:5002/api/statistics/gender-disease')
        this.statisticsData = response.data
        this.initChart()
      } catch (error) {
        console.error('Error fetching gender-disease statistics:', error)
        // 如果API调用失败，使用默认数据
        this.initChart()
      }
    },
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id))

      // 如果有统计数据，使用真实数据；否则使用默认数据
      const xData = this.statisticsData ? this.statisticsData.diseaseNames : ['正常', '糖尿病', '青光眼', '白内障', 'AMD', '高血压', '近视', '其他疾病']
      const femaleData = this.statisticsData ? this.statisticsData.femaleData : [497, 243, 90, 123, 75, 27, 110, 474]
      const maleData = this.statisticsData ? this.statisticsData.maleData : [639, 282, 125, 88, 86, 50, 64, 490]
      const totalData = this.statisticsData ? this.statisticsData.totalData : [1136, 525, 215, 211, 161, 77, 174, 964]

      this.chart.setOption({
        backgroundColor: '#344b58',
        title: {
          text: '统计数据',
          x: '20',
          top: '20',
          textStyle: {
            color: '#fff',
            fontSize: '22'
          },
          subtextStyle: {
            color: '#90979c',
            fontSize: '16'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            textStyle: {
              color: '#fff'
            }
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          borderWidth: 0,
          top: 150,
          bottom: 95,
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          x: '5%',
          top: '10%',
          textStyle: {
            color: '#90979c'
          },
          data: ['女性', '男性', '平均']
        },
        calculable: true,
        xAxis: [{
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#90979c'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitArea: {
            show: false
          },
          axisLabel: {
            interval: 0,
            rotate: 45, // 旋转标签以防止重叠
            textStyle: {
              color: '#fff'
            }
          },
          data: xData
        }],
        yAxis: [{
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: '#90979c'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#90979c'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: '#fff'
            }
          },
          splitArea: {
            show: false
          }
        }],
        dataZoom: [{
          show: true,
          height: 30,
          xAxisIndex: [
            0
          ],
          bottom: 30,
          start: 10,
          end: 80,
          handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
          handleSize: '110%',
          handleStyle: {
            color: '#d3dee5'

          },
          textStyle: {
            color: '#fff' },
          borderColor: '#90979c'

        }, {
          type: 'inside',
          show: true,
          height: 15,
          start: 1,
          end: 35
        }],
        series: [{
          name: '女性',
          type: 'bar',
          stack: '总量',
          barMaxWidth: 35,
          barGap: '10%',
          itemStyle: {
            normal: {
              color: 'rgba(255,144,128,1)',
              label: {
                show: true,
                textStyle: {
                  color: '#fff'
                },
                position: 'insideTop',
                formatter(p) {
                  return p.value > 0 ? p.value : ''
                }
              }
            }
          },
          data: femaleData
        },

        {
          name: '男性',
          type: 'bar',
          stack: '总量',
          itemStyle: {
            normal: {
              color: 'rgba(0,191,183,1)',
              barBorderRadius: 0,
              label: {
                show: true,
                position: 'top',
                formatter(p) {
                  return p.value > 0 ? p.value : ''
                }
              }
            }
          },
          data: maleData
        }, {
          name: '平均',
          type: 'line',
          stack: '总量',
          symbolSize: 10,
          symbol: 'circle',
          itemStyle: {
            normal: {
              color: 'rgba(252,230,48,1)',
              barBorderRadius: 0,
              label: {
                show: true,
                position: 'top',
                formatter(p) {
                  return p.value > 0 ? p.value : ''
                }
              }
            }
          },
          data: totalData
        }
        ]
      })
    }
  }
}
</script>
