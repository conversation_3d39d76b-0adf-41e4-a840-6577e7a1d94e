package com.example.demo.Service;

import com.example.demo.pojo.patientPojo.PatientRepository;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class StatisticsService {

    @Autowired
    private PatientRepository patientRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 疾病名称映射
    private final String[] diseaseNames = {
        "正常", "糖尿病", "青光眼", "白内障", "AMD", "高血压", "近视", "其他疾病"
    };

    /**
     * 获取年龄段-眼疾统计数据
     */
    public Map<String, Object> getAgeGroupDiseaseStatistics() {
        List<Object[]> rawData = patientRepository.findAgeGroupDiseaseStatistics();
        
        // 初始化年龄段
        String[] ageGroups = {"<=45", "45-55", "55-65", "65-75", ">75"};
        
        // 初始化统计结果
        Map<String, Map<String, Integer>> ageGroupStats = new LinkedHashMap<>();
        for (String ageGroup : ageGroups) {
            Map<String, Integer> diseaseCount = new LinkedHashMap<>();
            for (String disease : diseaseNames) {
                diseaseCount.put(disease, 0);
            }
            ageGroupStats.put(ageGroup, diseaseCount);
        }

        // 处理原始数据
        for (Object[] row : rawData) {
            String ageGroup = (String) row[0];
            String rightEyeCondition = (String) row[2];
            String leftEyeCondition = (String) row[3];

            // 处理右眼数据
            if (rightEyeCondition != null) {
                processConditionData(ageGroupStats.get(ageGroup), rightEyeCondition);
            }

            // 处理左眼数据
            if (leftEyeCondition != null) {
                processConditionData(ageGroupStats.get(ageGroup), leftEyeCondition);
            }
        }

        // 转换为前端需要的格式
        Map<String, Object> result = new HashMap<>();
        result.put("ageGroups", ageGroups);
        result.put("diseaseNames", diseaseNames);
        
        // 为每种疾病创建年龄段数据数组
        Map<String, List<Integer>> diseaseData = new LinkedHashMap<>();
        for (String disease : diseaseNames) {
            List<Integer> data = new ArrayList<>();
            for (String ageGroup : ageGroups) {
                data.add(ageGroupStats.get(ageGroup).get(disease));
            }
            diseaseData.put(disease, data);
        }
        result.put("diseaseData", diseaseData);

        return result;
    }

    /**
     * 获取性别-眼疾统计数据
     */
    public Map<String, Object> getGenderDiseaseStatistics() {
        List<Object[]> rawData = patientRepository.findGenderDiseaseStatistics();
        
        // 初始化统计结果
        Map<String, Integer> maleStats = new LinkedHashMap<>();
        Map<String, Integer> femaleStats = new LinkedHashMap<>();
        
        for (String disease : diseaseNames) {
            maleStats.put(disease, 0);
            femaleStats.put(disease, 0);
        }

        // 处理原始数据
        for (Object[] row : rawData) {
            String gender = (String) row[0];
            String rightEyeCondition = (String) row[1];
            String leftEyeCondition = (String) row[2];

            Map<String, Integer> targetStats = "man".equals(gender) ? maleStats : femaleStats;

            // 处理右眼数据
            if (rightEyeCondition != null) {
                processConditionData(targetStats, rightEyeCondition);
            }

            // 处理左眼数据
            if (leftEyeCondition != null) {
                processConditionData(targetStats, leftEyeCondition);
            }
        }

        // 转换为前端需要的格式
        Map<String, Object> result = new HashMap<>();
        result.put("diseaseNames", diseaseNames);
        
        List<Integer> maleData = new ArrayList<>();
        List<Integer> femaleData = new ArrayList<>();
        List<Integer> totalData = new ArrayList<>();
        
        for (String disease : diseaseNames) {
            int maleCount = maleStats.get(disease);
            int femaleCount = femaleStats.get(disease);
            
            maleData.add(maleCount);
            femaleData.add(femaleCount);
            totalData.add(maleCount + femaleCount);
        }
        
        result.put("maleData", maleData);
        result.put("femaleData", femaleData);
        result.put("totalData", totalData);

        return result;
    }

    /**
     * 处理眼疾条件数据
     */
    private void processConditionData(Map<String, Integer> stats, String conditionJson) {
        try {
            List<Integer> conditions = objectMapper.readValue(conditionJson, new TypeReference<List<Integer>>() {});
            
            for (int i = 0; i < conditions.size() && i < diseaseNames.length; i++) {
                if (conditions.get(i) == 1) {
                    String diseaseName = diseaseNames[i];
                    stats.put(diseaseName, stats.get(diseaseName) + 1);
                }
            }
        } catch (Exception e) {
            // 忽略JSON解析错误
            System.err.println("Error parsing condition JSON: " + conditionJson);
        }
    }
}
