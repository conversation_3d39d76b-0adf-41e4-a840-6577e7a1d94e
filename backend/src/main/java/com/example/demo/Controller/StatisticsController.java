package com.example.demo.Controller;

import com.example.demo.Service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/api/statistics")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 获取年龄段-眼疾统计数据
     * 用于眼疾-年龄曲线图
     */
    @GetMapping("/age-disease")
    public ResponseEntity<Map<String, Object>> getAgeDiseaseStatistics() {
        try {
            Map<String, Object> statistics = statisticsService.getAgeGroupDiseaseStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            System.err.println("Error getting age-disease statistics: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 获取性别-眼疾统计数据
     * 用于眼疾-性别分布图
     */
    @GetMapping("/gender-disease")
    public ResponseEntity<Map<String, Object>> getGenderDiseaseStatistics() {
        try {
            Map<String, Object> statistics = statisticsService.getGenderDiseaseStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            System.err.println("Error getting gender-disease statistics: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).build();
        }
    }
}
